package com.ybm100.app.crm.utils;

import android.content.Context;
import android.util.Log;

import java.util.HashMap;
import java.util.Map;

/**
 * QT SDK埋点辅助类
 * 提供简化的埋点接口，统一管理埋点调用
 */
public class QTTrackingHelper {
    private static final String TAG = "QTTrackingHelper";

    /**
     * 自定义事件埋点
     * 
     * @param context 上下文
     * @param eventName 事件名称
     * @param properties 事件属性
     */
    public static void trackEvent(Context context, String eventName, Map<String, Object> properties) {
        try {
            Class<?> qtTrackAgentClass = Class.forName("com.lydaas.qtsdk.QtTrackAgent");
            java.lang.reflect.Method onEventMethod = qtTrackAgentClass.getMethod("onEventObject", 
                Context.class, String.class, Map.class);
            
            onEventMethod.invoke(null, context, eventName, properties != null ? properties : new HashMap<>());
            Log.d(TAG, "Event tracked: " + eventName + " with properties: " + properties);
        } catch (Exception e) {
            Log.e(TAG, "Failed to track event: " + eventName, e);
        }
    }

    /**
     * 页面开始埋点
     * 
     * @param context 上下文
     * @param pageName 页面名称
     */
    public static void onPageStart(Context context, String pageName) {
        try {
            Class<?> qtTrackAgentClass = Class.forName("com.lydaas.qtsdk.QtTrackAgent");
            java.lang.reflect.Method onPageStartMethod = qtTrackAgentClass.getMethod("onPageStart", 
                Context.class, String.class);
            
            onPageStartMethod.invoke(null, context, pageName);
            Log.d(TAG, "Page started: " + pageName);
        } catch (Exception e) {
            Log.e(TAG, "Failed to track page start: " + pageName, e);
        }
    }

    /**
     * 页面结束埋点
     * 
     * @param context 上下文
     * @param pageName 页面名称
     */
    public static void onPageEnd(Context context, String pageName) {
        try {
            Class<?> qtTrackAgentClass = Class.forName("com.lydaas.qtsdk.QtTrackAgent");
            java.lang.reflect.Method onPageEndMethod = qtTrackAgentClass.getMethod("onPageEnd", 
                Context.class, String.class);
            
            onPageEndMethod.invoke(null, context, pageName);
            Log.d(TAG, "Page ended: " + pageName);
        } catch (Exception e) {
            Log.e(TAG, "Failed to track page end: " + pageName, e);
        }
    }

    /**
     * 用户登录埋点
     * 
     * @param context 上下文
     * @param userId 用户ID
     */
    public static void onUserLogin(Context context, String userId) {
        try {
            Class<?> qtTrackAgentClass = Class.forName("com.lydaas.qtsdk.QtTrackAgent");
            java.lang.reflect.Method onProfileSignInMethod = qtTrackAgentClass.getMethod("onProfileSignIn", 
                Context.class, String.class);
            
            onProfileSignInMethod.invoke(null, context, userId);
            Log.d(TAG, "User login tracked: " + userId);
        } catch (Exception e) {
            Log.e(TAG, "Failed to track user login: " + userId, e);
        }
    }

    /**
     * 用户登出埋点
     * 
     * @param context 上下文
     */
    public static void onUserLogout(Context context) {
        try {
            Class<?> qtTrackAgentClass = Class.forName("com.lydaas.qtsdk.QtTrackAgent");
            java.lang.reflect.Method onProfileSignOffMethod = qtTrackAgentClass.getMethod("onProfileSignOff", 
                Context.class);
            
            onProfileSignOffMethod.invoke(null, context);
            Log.d(TAG, "User logout tracked");
        } catch (Exception e) {
            Log.e(TAG, "Failed to track user logout", e);
        }
    }

    /**
     * 应用强制退出前调用
     * 用于保存统计数据
     * 
     * @param context 上下文
     */
    public static void onKillProcess(Context context) {
        try {
            Class<?> qtTrackAgentClass = Class.forName("com.lydaas.qtsdk.QtTrackAgent");
            java.lang.reflect.Method onKillProcessMethod = qtTrackAgentClass.getMethod("onKillProcess", 
                Context.class);
            
            onKillProcessMethod.invoke(null, context);
            Log.d(TAG, "Kill process tracked");
        } catch (Exception e) {
            Log.e(TAG, "Failed to track kill process", e);
        }
    }

    /**
     * 开启数据采集
     */
    public static void enableSDK() {
        try {
            Class<?> qtTrackAgentClass = Class.forName("com.lydaas.qtsdk.QtTrackAgent");
            java.lang.reflect.Method enableSDKMethod = qtTrackAgentClass.getMethod("enableSDK");
            
            enableSDKMethod.invoke(null);
            Log.d(TAG, "SDK enabled");
        } catch (Exception e) {
            Log.e(TAG, "Failed to enable SDK", e);
        }
    }

    /**
     * 关闭数据采集
     */
    public static void disableSDK() {
        try {
            Class<?> qtTrackAgentClass = Class.forName("com.lydaas.qtsdk.QtTrackAgent");
            java.lang.reflect.Method disableSDKMethod = qtTrackAgentClass.getMethod("disableSDK");
            
            disableSDKMethod.invoke(null);
            Log.d(TAG, "SDK disabled");
        } catch (Exception e) {
            Log.e(TAG, "Failed to disable SDK", e);
        }
    }

    // 常用事件埋点的便捷方法

    /**
     * 按钮点击埋点
     */
    public static void trackButtonClick(Context context, String buttonName, String pageName) {
        Map<String, Object> properties = new HashMap<>();
        properties.put("button_name", buttonName);
        properties.put("page_name", pageName);
        trackEvent(context, "button_click", properties);
    }

    /**
     * 页面浏览埋点
     */
    public static void trackPageView(Context context, String pageName, String pageType) {
        Map<String, Object> properties = new HashMap<>();
        properties.put("page_name", pageName);
        properties.put("page_type", pageType);
        trackEvent(context, "page_view", properties);
    }

    /**
     * 搜索埋点
     */
    public static void trackSearch(Context context, String keyword, String searchType) {
        Map<String, Object> properties = new HashMap<>();
        properties.put("keyword", keyword);
        properties.put("search_type", searchType);
        trackEvent(context, "search", properties);
    }
}
