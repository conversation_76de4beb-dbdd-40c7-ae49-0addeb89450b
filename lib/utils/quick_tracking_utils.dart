import 'package:qt_common_sdk/qt_common_sdk.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// Quick Tracking SDK工具类
/// 用于统一管理阿里云Quick Tracking统计分析SDK的初始化和使用
class QuickTrackingUtils {
  static bool _isInitialized = false;
  static const String _privacyConsentKey = 'qt_privacy_consent';
  
  /// 初始化Quick Tracking SDK
  /// 注意：只有在用户同意隐私政策后才能调用此方法
  static Future<void> initSDK() async {
    if (_isInitialized) {
      print('Quick Tracking SDK already initialized');
      return;
    }
    
    try {
      // 检查用户是否同意隐私政策
      bool hasConsent = await checkUserPrivacyConsent();
      if (!hasConsent) {
        print('User has not consented to privacy policy, skipping SDK initialization');
        return;
      }
      
      // 获取当前环境信息来决定使用哪个AppKey
      String androidAppKey = _getAndroidAppKey();
      String iosAppKey = _getIOSAppKey();
      String channel = _getChannel();
      
      if (androidAppKey.isEmpty || iosAppKey.isEmpty) {
        print('AppKey is empty, skipping SDK initialization');
        return;
      }
      
      // 设置日志开关（仅在调试模式下开启）
      await QTCommonSdk.setLogEnabled(true); // 生产环境请设置为false
      
      // 初始化SDK
      await QTCommonSdk.initCommon(androidAppKey, iosAppKey, channel);
      
      _isInitialized = true;
      print('Quick Tracking SDK initialized successfully');
      print('Android AppKey: $androidAppKey');
      print('iOS AppKey: $iosAppKey');
      print('Channel: $channel');
      
    } catch (e) {
      print('Failed to initialize Quick Tracking SDK: $e');
    }
  }
  
  /// 自定义事件统计
  /// 
  /// @param eventName 事件名称
  /// @param properties 事件属性
  static Future<void> trackEvent(String eventName, [Map<String, dynamic>? properties]) async {
    if (!_isInitialized) {
      print('SDK not initialized, skipping event: $eventName');
      return;
    }
    
    try {
      await QTCommonSdk.onEvent(eventName, properties ?? {});
      print('Event tracked: $eventName with properties: $properties');
    } catch (e) {
      print('Failed to track event: $eventName, error: $e');
    }
  }
  
  /// 带页面信息的自定义事件统计
  /// 
  /// @param eventName 事件名称
  /// @param pageName 页面名称
  /// @param properties 事件属性
  static Future<void> trackEventWithPage(String eventName, String pageName, [Map<String, dynamic>? properties]) async {
    if (!_isInitialized) {
      print('SDK not initialized, skipping event: $eventName');
      return;
    }
    
    try {
      await QTCommonSdk.onEventWithPage(eventName, pageName, properties ?? {});
      print('Event tracked: $eventName on page: $pageName with properties: $properties');
    } catch (e) {
      print('Failed to track event: $eventName on page: $pageName, error: $e');
    }
  }
  
  /// 页面开始统计
  /// 
  /// @param pageName 页面名称
  static Future<void> onPageStart(String pageName) async {
    if (!_isInitialized) {
      return;
    }
    
    try {
      await QTCommonSdk.onPageStart(pageName);
      print('Page started: $pageName');
    } catch (e) {
      print('Failed to track page start: $pageName, error: $e');
    }
  }
  
  /// 页面结束统计
  /// 
  /// @param pageName 页面名称
  static Future<void> onPageEnd(String pageName) async {
    if (!_isInitialized) {
      return;
    }
    
    try {
      await QTCommonSdk.onPageEnd(pageName);
      print('Page ended: $pageName');
    } catch (e) {
      print('Failed to track page end: $pageName, error: $e');
    }
  }
  
  /// 用户登录统计
  /// 
  /// @param userId 用户ID
  static Future<void> onUserLogin(String userId) async {
    if (!_isInitialized) {
      return;
    }
    
    try {
      await QTCommonSdk.onProfileSignIn(userId);
      print('User login tracked: $userId');
    } catch (e) {
      print('Failed to track user login: $userId, error: $e');
    }
  }
  
  /// 用户登出统计
  static Future<void> onUserLogout() async {
    if (!_isInitialized) {
      return;
    }
    
    try {
      await QTCommonSdk.onProfileSignOff();
      print('User logout tracked');
    } catch (e) {
      print('Failed to track user logout, error: $e');
    }
  }
  
  /// 设置页面属性
  /// 
  /// @param pageName 页面名称
  /// @param properties 页面属性
  static Future<void> setPageProperty(String pageName, Map<String, dynamic> properties) async {
    if (!_isInitialized) {
      return;
    }
    
    try {
      await QTCommonSdk.setPageProperty(pageName, properties);
      print('Page property set for: $pageName with properties: $properties');
    } catch (e) {
      print('Failed to set page property for: $pageName, error: $e');
    }
  }
  
  /// 注册全局属性
  /// 
  /// @param properties 全局属性
  static Future<void> registerGlobalProperties(Map<String, dynamic> properties) async {
    if (!_isInitialized) {
      return;
    }
    
    try {
      await QTCommonSdk.registerGlobalProperties(properties);
      print('Global properties registered: $properties');
    } catch (e) {
      print('Failed to register global properties, error: $e');
    }
  }
  
  /// 删除一个全局属性
  /// 
  /// @param propertyName 属性名称
  static Future<void> unregisterGlobalProperty(String propertyName) async {
    if (!_isInitialized) {
      return;
    }
    
    try {
      await QTCommonSdk.unregisterGlobalProperty(propertyName);
      print('Global property unregistered: $propertyName');
    } catch (e) {
      print('Failed to unregister global property: $propertyName, error: $e');
    }
  }
  
  /// 清除全局属性
  static Future<void> clearGlobalProperties() async {
    if (!_isInitialized) {
      return;
    }
    
    try {
      await QTCommonSdk.clearGlobalProperties();
      print('Global properties cleared');
    } catch (e) {
      print('Failed to clear global properties, error: $e');
    }
  }
  
  /// 检查用户隐私政策同意状态
  static Future<bool> checkUserPrivacyConsent() async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      return prefs.getBool(_privacyConsentKey) ?? false;
    } catch (e) {
      print('Failed to check privacy consent: $e');
      return false;
    }
  }
  
  /// 设置用户隐私政策同意状态
  static Future<void> setUserPrivacyConsent(bool consent) async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_privacyConsentKey, consent);
      
      // 如果用户同意隐私政策且SDK未初始化，则立即初始化
      if (consent && !_isInitialized) {
        await initSDK();
      }
    } catch (e) {
      print('Failed to set privacy consent: $e');
    }
  }
  
  /// 获取Android AppKey
  /// 根据当前环境返回对应的AppKey
  static String _getAndroidAppKey() {
    if (_isProductionEnvironment()) {
      // 生产环境AppKey - 请在阿里云控制台申请并替换此处的AppKey
      return "your_android_production_appkey_here";
    } else {
      // 测试环境AppKey - 请在阿里云控制台申请并替换此处的AppKey
      return "your_android_test_appkey_here";
    }
  }
  
  /// 获取iOS AppKey
  /// 根据当前环境返回对应的AppKey
  static String _getIOSAppKey() {
    if (_isProductionEnvironment()) {
      // 生产环境AppKey - 请在阿里云控制台申请并替换此处的AppKey
      return "your_ios_production_appkey_here";
    } else {
      // 测试环境AppKey - 请在阿里云控制台申请并替换此处的AppKey
      return "your_ios_test_appkey_here";
    }
  }
  
  /// 获取渠道标识
  static String _getChannel() {
    if (_isProductionEnvironment()) {
      return "official"; // 生产环境渠道
    } else {
      return "test"; // 测试环境渠道
    }
  }
  
  /// 判断是否为生产环境
  /// 这里需要根据您的项目实际情况来判断环境
  static bool _isProductionEnvironment() {
    // TODO: 根据您的项目实际情况来判断环境
    // 可以通过BuildConfig、环境变量或其他方式来判断
    return false; // 默认为测试环境，请根据实际情况修改
  }
  
  /// 检查SDK是否已初始化
  static bool get isInitialized => _isInitialized;
}
